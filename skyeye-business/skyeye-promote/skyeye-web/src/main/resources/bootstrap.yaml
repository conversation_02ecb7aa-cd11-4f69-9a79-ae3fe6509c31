server:
  port: 8081

spring:
  application:
    name: skyeye-pro-${spring.profiles.active} # 服务名
  main:
    allow-circular-references: true # 允许循环依赖，因为项目是三层架构，无法避免这个情况。
    allow-bean-definition-overriding: true # 允许 Bean 覆盖，例如说 Feign 等会存在重复定义的服务
  data:
    redis:
      repositories:
        enabled: false # 项目未使用到 Spring Data Redis 的 Repository，所以直接禁用，保证启动速度
  profiles:
    active: dev
  # 数据库配置
  datasource:
    url: *******************************************************************************************************************************************
    username: erp
    password: erp
    driver-class-name: com.mysql.cj.jdbc.Driver
    type: com.alibaba.druid.pool.DruidDataSource
    # Druid连接池配置
    druid:
      initial-size: 5
      min-idle: 5
      max-active: 20
      max-wait: 60000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: SELECT 1 FROM DUAL
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
  cloud:
    nacos:
      discovery:
        server-addr: 172.18.92.40:8848 # 配置服务注册nacos地址
        namespace: ${spring.profiles.active} # 配置命名空间
        service: ${spring.application.name} # 配置服务名
      config:
        # 指定nacos server的地址
        server-addr: 172.18.92.40:8848
        # 配置文件后缀
        file-extension: yml
        # 命名空间 常用场景之一是不同环境的配置的区分隔离，例如开发测试环境和生产环境的资源（如配置、服务）隔离等
        namespace: ${spring.profiles.active}
        # 支持多个共享 Data Id 的配置，优先级小于ext-config,自定义 Data Id 配置 属性是个集合，内部由 Config POJO 组成。Config 有 3 个属性，分别是 dataId, group 以及 refresh
        ext-config:
          - data-id: skyeye-common.yml # 配置文件名-Data Id
            group: DEFAULT_GROUP # 默认为DEFAULT_GROUP
            refresh: false # 是否动态刷新，默认为false

# 配置打印sql到控制台
logging:
  level:
    com.skyeye: debug

webroot:
  # 工作流相关的服务
  skyeye-flowable: skyeye-flowable-${spring.profiles.active}
  # 报表
  skyeye-report: skyeye-report-${spring.profiles.active}
  # xxl-job
  xxl-job: xxl-job-${spring.profiles.active}

topic:
  # 邮件通知的topic
  ordinary-mail-delivery-service: ORDINARY_MAIL_DELIVERY_SERVICE
  # 消息通知的topic
  notice-send-service: NOTICE_SEND_SERVICE
  # 短信通知的topic
  sms-send-service: SMS_SEND_SERVICE

skyeye:
  sms-code: # 短信验证码相关的配置项
    expire-times: 10m
    send-frequency: 1m
    send-maximum-quantity-per-day: 10
    begin-code: 1 # 这里配置 9999 的原因是，测试方便。
    end-code: 9999 # 这里配置 9999 的原因是，测试方便。