#!/bin/bash

# Nacos连接测试脚本
# 用于验证Nacos服务器的HTTP和gRPC连接

NACOS_HOST="*************"
HTTP_PORT="8848"
GRPC_PORT="9848"

echo "=== Nacos连接测试 ==="
echo "测试服务器: $NACOS_HOST"
echo

# 测试HTTP连接 (Web控制台)
echo "1. 测试HTTP连接 (端口 $HTTP_PORT)..."
if curl -s --connect-timeout 5 "http://$NACOS_HOST:$HTTP_PORT/nacos/v1/console/health" > /dev/null 2>&1; then
    echo "✓ HTTP连接正常"
else
    echo "✗ HTTP连接失败"
fi

# 测试gRPC连接 (客户端连接)
echo
echo "2. 测试gRPC连接 (端口 $GRPC_PORT)..."
if timeout 5 bash -c "</dev/tcp/$NACOS_HOST/$GRPC_PORT" 2>/dev/null; then
    echo "✓ gRPC端口可访问"
else
    echo "✗ gRPC端口不可访问 - 这可能是问题所在！"
fi

# 测试Nacos API
echo
echo "3. 测试Nacos API..."
response=$(curl -s --connect-timeout 5 "http://$NACOS_HOST:$HTTP_PORT/nacos/v1/ns/operator/servers")
if [ $? -eq 0 ] && [ -n "$response" ]; then
    echo "✓ Nacos API响应正常"
    echo "服务器信息: $response"
else
    echo "✗ Nacos API无响应"
fi

# 测试命名空间
echo
echo "4. 测试dev命名空间..."
namespace_response=$(curl -s --connect-timeout 5 "http://$NACOS_HOST:$HTTP_PORT/nacos/v1/console/namespaces")
if [ $? -eq 0 ] && echo "$namespace_response" | grep -q "dev"; then
    echo "✓ dev命名空间存在"
else
    echo "⚠ dev命名空间可能不存在，请检查Nacos控制台"
fi

echo
echo "=== 测试完成 ==="
echo
echo "如果gRPC端口不可访问，请检查："
echo "1. Nacos服务器是否启用了gRPC端口9848"
echo "2. 防火墙是否阻止了9848端口"
echo "3. Nacos配置文件中的server.port和grpc.port设置"
