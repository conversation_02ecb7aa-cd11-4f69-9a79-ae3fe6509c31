#!/bin/bash

# 检查Nacos端口连通性
NACOS_HOST="*************"

echo "=== 检查Nacos端口连通性 ==="
echo

# 检查HTTP端口8848
echo "检查HTTP端口8848..."
if timeout 5 bash -c "</dev/tcp/$NACOS_HOST/8848" 2>/dev/null; then
    echo "✓ 端口8848可访问"
else
    echo "✗ 端口8848不可访问"
fi

# 检查gRPC端口9848
echo "检查gRPC端口9848..."
if timeout 5 bash -c "</dev/tcp/$NACOS_HOST/9848" 2>/dev/null; then
    echo "✓ 端口9848可访问"
else
    echo "✗ 端口9848不可访问 - 这是问题所在！"
    echo
    echo "解决方案："
    echo "1. 在Nacos服务器上开放9848端口"
    echo "2. 检查防火墙设置"
    echo "3. 确认Nacos配置文件中的grpc端口设置"
fi

echo
echo "=== 检查完成 ==="
