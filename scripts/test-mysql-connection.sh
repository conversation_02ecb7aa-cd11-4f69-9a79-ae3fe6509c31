#!/bin/bash

# MySQL连接测试脚本
# 用于诊断xxl-job数据库连接问题

echo "=== MySQL连接诊断 ==="
echo

# 测试目标
MYSQL_HOST="*************"
MYSQL_PORT="3306"

echo "测试目标: $MYSQL_HOST:$MYSQL_PORT"
echo

# 1. 测试网络连通性
echo "1. 测试网络连通性..."
if ping -c 3 "$MYSQL_HOST" >/dev/null 2>&1; then
    echo "✅ 主机 $MYSQL_HOST 网络可达"
else
    echo "❌ 主机 $MYSQL_HOST 网络不可达"
    echo "请检查网络连接和主机IP地址"
fi

# 2. 测试端口连通性
echo
echo "2. 测试MySQL端口连通性..."
if timeout 5 bash -c "</dev/tcp/$MYSQL_HOST/$MYSQL_PORT" 2>/dev/null; then
    echo "✅ MySQL端口 $MYSQL_HOST:$MYSQL_PORT 可访问"
else
    echo "❌ MySQL端口 $MYSQL_HOST:$MYSQL_PORT 不可访问"
    echo "可能的原因："
    echo "  - MySQL服务未启动"
    echo "  - 防火墙阻止了3306端口"
    echo "  - MySQL配置不允许远程连接"
fi

# 3. 测试MySQL服务状态（如果有mysql客户端）
echo
echo "3. 测试MySQL服务..."
if command -v mysql >/dev/null 2>&1; then
    echo "尝试连接MySQL服务..."
    # 尝试连接（不输入密码，只测试连接）
    if timeout 10 mysql -h"$MYSQL_HOST" -P"$MYSQL_PORT" -uroot -e "SELECT 1;" 2>/dev/null; then
        echo "✅ MySQL服务正常"
    else
        echo "⚠️  MySQL连接需要密码或有其他问题"
        echo "请手动测试: mysql -h$MYSQL_HOST -P$MYSQL_PORT -uroot -p"
    fi
else
    echo "⚠️  未安装mysql客户端，无法测试MySQL连接"
    echo "可以安装: apt-get install mysql-client 或 yum install mysql"
fi

echo
echo "=== 诊断完成 ==="
echo
echo "如果端口不可访问，请检查："
echo "1. MySQL服务是否在 $MYSQL_HOST 上运行"
echo "2. MySQL是否监听在0.0.0.0:3306（允许远程连接）"
echo "3. 防火墙是否开放3306端口"
echo "4. Docker容器的端口映射是否正确"
