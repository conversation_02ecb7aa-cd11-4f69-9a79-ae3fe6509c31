server:
  port: 7000

spring:
  application:
    name: skyeye-zuul-${spring.profiles.active} # 服务名
  main:
    allow-circular-references: true # 允许循环依赖，因为项目是三层架构，无法避免这个情况。
    web-application-type: reactive
  profiles:
    active: dev
  cloud:
    nacos:
      discovery:
        server-addr: 192.168.31.71:8848 # 配置服务注册nacos地址
        namespace: ${spring.profiles.active} # 配置命名空间
        service: ${spring.application.name} # 配置服务名
      config:
        # 指定nacos server的地址
        server-addr: 192.168.31.71:8848
        # 配置文件后缀
        file-extension: yml
        # 命名空间 常用场景之一是不同环境的配置的区分隔离，例如开发测试环境和生产环境的资源（如配置、服务）隔离等
        namespace: ${spring.profiles.active} # 配置命名空间
        # 支持多个共享 Data Id 的配置，优先级小于ext-config,自定义 Data Id 配置 属性是个集合，内部由 Config POJO 组成。Config 有 3 个属性，分别是 dataId, group 以及 refresh
        ext-config:
          - data-id: skyeye-common.yml # 配置文件名-Data Id
            group: DEFAULT_GROUP # 默认为DEFAULT_GROUP
            refresh: false # 是否动态刷新，默认为false
    gateway:
      routes:
        - id: fileBase    #路由的ID，没有固定规则但要求唯一，建议配合服务名
          uri: lb://skyeye-pro-${spring.profiles.active} #匹配后提供服务的路由地址
          predicates:
            - Path=/${spring.profiles.active}/fileBase/**        # 断言，路径相匹配的进行路由
          filters:
            - StripPrefix=2
        - id: reqBase
          uri: lb://skyeye-pro-${spring.profiles.active}
          predicates:
            - Path=/${spring.profiles.active}/reqBase/**
          filters:
            - StripPrefix=2
        - id: shop
          uri: lb://skyeye-shop-${spring.profiles.active}
          predicates:
            - Path=/${spring.profiles.active}/shop/**
          filters:
            - StripPrefix=2
        - id: flowable
          uri: lb://skyeye-flowable-${spring.profiles.active}
          predicates:
            - Path=/${spring.profiles.active}/flowable/**
          filters:
            - StripPrefix=2
        - id: school
          uri: lb://skyeye-school-${spring.profiles.active}
          predicates:
            - Path=/${spring.profiles.active}/school/**
          filters:
            - StripPrefix=2
        - id: report
          uri: lb://skyeye-report-${spring.profiles.active}
          predicates:
            - Path=/${spring.profiles.active}/report/**
          filters:
            - StripPrefix=2
        - id: adm
          uri: lb://skyeye-adm-${spring.profiles.active}
          predicates:
            - Path=/${spring.profiles.active}/adm/**,/${spring.profiles.active}/survey/**,/${spring.profiles.active}/note/**,/${spring.profiles.active}/knowlg/**,/${spring.profiles.active}/mail/**,/${spring.profiles.active}/diskCloud/**,/${spring.profiles.active}/email/**,/${spring.profiles.active}/notice/**,/${spring.profiles.active}/businessFlow/**,/${spring.profiles.active}/forum/**,/${spring.profiles.active}/jobdiary/**,/${spring.profiles.active}/ehr/**,/${spring.profiles.active}/lightApp/**,/${spring.profiles.active}/schedule/**
          filters:
            - StripPrefix=2
        - id: wages
          uri: lb://skyeye-wages-${spring.profiles.active}
          predicates:
            - Path=/${spring.profiles.active}/wages/**
          filters:
            - StripPrefix=2
        - id: crm
          uri: lb://skyeye-crm-${spring.profiles.active}
          predicates:
            - Path=/${spring.profiles.active}/crm/**
          filters:
            - StripPrefix=2
        - id: ifs
          uri: lb://skyeye-ifs-${spring.profiles.active}
          predicates:
            - Path=/${spring.profiles.active}/ifs/**
          filters:
            - StripPrefix=2
        - id: erp
          uri: lb://skyeye-erp-${spring.profiles.active}
          predicates:
            - Path=/${spring.profiles.active}/erp/**
          filters:
            - StripPrefix=2
        - id: boss
          uri: lb://skyeye-boss-${spring.profiles.active}
          predicates:
            - Path=/${spring.profiles.active}/boss/**
          filters:
            - StripPrefix=2
        - id: checkwork
          uri: lb://skyeye-checkwork-${spring.profiles.active}
          predicates:
            - Path=/${spring.profiles.active}/checkwork/**
          filters:
            - StripPrefix=2
        - id: sealService
          uri: lb://skyeye-seal-service-${spring.profiles.active}
          predicates:
            - Path=/${spring.profiles.active}/sealService/**
          filters:
            - StripPrefix=2
        - id: wall
          uri: lb://skyeye-wall-${spring.profiles.active}
          predicates:
            - Path=/${spring.profiles.active}/wall/**
          filters:
            - StripPrefix=2
        - id: tms
          uri: lb://skyeye-tms-${spring.profiles.active}
          predicates:
            - Path=/${spring.profiles.active}/tms/**
          filters:
            - StripPrefix=2
        - id: project
          uri: lb://skyeye-project-${spring.profiles.active}
          predicates:
            - Path=/${spring.profiles.active}/project/**
          filters:
            - StripPrefix=2
        - id: ai
          uri: lb://skyeye-ai-${spring.profiles.active}
          predicates:
            - Path=/${spring.profiles.active}/ai/**
          filters:
            - StripPrefix=2
      default-filters:
        - DedupeResponseHeader=Access-Control-Allow-Origin
        - AddResponseHeader=Access-Control-Allow-Origin, *  # 允许跨域
logging:
  level:
    com: debug