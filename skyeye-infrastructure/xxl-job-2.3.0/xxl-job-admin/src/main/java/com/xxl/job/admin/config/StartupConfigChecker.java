package com.xxl.job.admin.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.DatabaseMetaData;

/**
 * 启动时配置检查器
 * 在应用启动完成后检查关键配置和数据库连接
 */
@Component
public class StartupConfigChecker implements CommandLineRunner {
    
    private static final Logger logger = LoggerFactory.getLogger(StartupConfigChecker.class);
    
    @Autowired
    private Environment environment;
    
    @Autowired(required = false)
    private DataSource dataSource;
    
    @Override
    public void run(String... args) throws Exception {
        logger.info("=== XXL-JOB 启动配置检查 ===");
        
        // 检查环境配置
        checkEnvironmentConfig();
        
        // 检查数据库连接
        checkDatabaseConnection();
        
        logger.info("=== 配置检查完成 ===");
    }
    
    private void checkEnvironmentConfig() {
        logger.info("环境配置检查:");
        
        String profile = environment.getProperty("spring.profiles.active");
        logger.info("  当前环境: {}", profile);
        
        String appName = environment.getProperty("spring.application.name");
        logger.info("  应用名称: {}", appName);
        
        String nacosServer = environment.getProperty("spring.cloud.nacos.config.server-addr");
        logger.info("  Nacos服务器: {}", nacosServer);
        
        String namespace = environment.getProperty("spring.cloud.nacos.config.namespace");
        logger.info("  Nacos命名空间: {}", namespace);
        
        // 检查是否有数据库配置
        String datasourceUrl = environment.getProperty("spring.datasource.url");
        String jdbcPath = environment.getProperty("jdbc.database.path");
        
        if (datasourceUrl != null) {
            logger.info("  数据库URL (spring.datasource.url): {}", datasourceUrl);
        } else if (jdbcPath != null) {
            logger.info("  数据库路径 (jdbc.database.path): {}", jdbcPath);
        } else {
            logger.error("  ❌ 未找到数据库配置！");
        }
    }
    
    private void checkDatabaseConnection() {
        logger.info("数据库连接检查:");
        
        if (dataSource == null) {
            logger.error("  ❌ DataSource未配置或注入失败！");
            return;
        }
        
        try (Connection connection = dataSource.getConnection()) {
            DatabaseMetaData metaData = connection.getMetaData();
            logger.info("  ✅ 数据库连接成功");
            logger.info("  数据库类型: {}", metaData.getDatabaseProductName());
            logger.info("  数据库版本: {}", metaData.getDatabaseProductVersion());
            logger.info("  连接URL: {}", metaData.getURL());
            logger.info("  用户名: {}", metaData.getUserName());
            
            // 检查xxl-job相关表是否存在
            checkXxlJobTables(connection);
            
        } catch (Exception e) {
            logger.error("  ❌ 数据库连接失败: {}", e.getMessage());
            logger.error("  错误详情: ", e);
        }
    }
    
    private void checkXxlJobTables(Connection connection) {
        try {
            String[] tables = {"xxl_job_info", "xxl_job_log", "xxl_job_user", "xxl_job_group"};
            
            logger.info("  检查XXL-JOB表:");
            for (String table : tables) {
                boolean exists = connection.getMetaData().getTables(null, null, table, null).next();
                if (exists) {
                    logger.info("    ✅ 表 {} 存在", table);
                } else {
                    logger.warn("    ⚠️  表 {} 不存在", table);
                }
            }
        } catch (Exception e) {
            logger.warn("  检查XXL-JOB表时出错: {}", e.getMessage());
        }
    }
}
