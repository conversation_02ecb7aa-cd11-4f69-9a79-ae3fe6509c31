package com.xxl.job.admin.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.context.event.ApplicationEnvironmentPreparedEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.core.env.ConfigurableEnvironment;
import org.springframework.core.env.PropertySource;
import org.springframework.stereotype.Component;

/**
 * 配置调试监听器
 * 用于在应用启动时输出所有配置信息，帮助诊断配置问题
 */
@Component
public class ConfigDebugListener implements ApplicationListener<ApplicationEnvironmentPreparedEvent> {
    
    private static final Logger logger = LoggerFactory.getLogger(ConfigDebugListener.class);
    
    @Override
    public void onApplicationEvent(ApplicationEnvironmentPreparedEvent event) {
        ConfigurableEnvironment environment = event.getEnvironment();
        
        logger.info("=== XXL-JOB 配置调试信息 ===");
        
        // 输出所有PropertySource
        logger.info("所有配置源:");
        for (PropertySource<?> propertySource : environment.getPropertySources()) {
            logger.info("  - {}: {}", propertySource.getName(), propertySource.getClass().getSimpleName());
        }
        
        // 输出关键配置
        logger.info("关键配置信息:");
        logProperty(environment, "spring.profiles.active");
        logProperty(environment, "spring.application.name");
        logProperty(environment, "spring.cloud.nacos.config.server-addr");
        logProperty(environment, "spring.cloud.nacos.config.namespace");
        
        // 输出数据库相关配置
        logger.info("数据库配置:");
        logProperty(environment, "spring.datasource.url");
        logProperty(environment, "spring.datasource.username");
        logProperty(environment, "spring.datasource.password");
        logProperty(environment, "spring.datasource.driver-class-name");
        
        // 输出自定义数据库配置
        logger.info("自定义数据库配置:");
        logProperty(environment, "jdbc.database.name");
        logProperty(environment, "jdbc.database.address");
        logProperty(environment, "jdbc.database.path");
        logProperty(environment, "jdbc.database.username");
        logProperty(environment, "jdbc.database.password");
        
        logger.info("=== 配置调试信息结束 ===");
    }
    
    private void logProperty(ConfigurableEnvironment environment, String key) {
        String value = environment.getProperty(key);
        if (value != null) {
            // 隐藏密码信息
            if (key.toLowerCase().contains("password")) {
                value = "***";
            }
            logger.info("  {}: {}", key, value);
        } else {
            logger.warn("  {}: <未配置>", key);
        }
    }
}
